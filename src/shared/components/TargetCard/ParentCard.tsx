import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Trash } from 'react-bootstrap-icons';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Container,
  IconWrapper,
  Typography,
} from '@ghq-abi/design-system-v2';

import { Target } from '~/shared/types/Target';
import { cn } from '~/shared/utils/cn';

import { Scale } from '../icons';

interface ParentCardProps {
  data: Target;
  onRemoveActionClick?: (target: Target[]) => void;
  isDrawer?: boolean;
}

export function ParentCard({
  data,
  onRemoveActionClick,
  isDrawer,
}: ParentCardProps) {
  return (
    <Card.Header className="flex gap-4 pb-1 ">
      <Card.Title className="flex gap-4">
        <IconWrapper variant="secondary" round="md" size={42}>
          <BarChartFill size={24} />
        </IconWrapper>
        <Container
          className={cn('flex flex-col max-w-4xl', {
            'max-w-xs': isDrawer,
          })}
        >
          <Typography
            variant="body-md-regular"
            color="dark"
            className="font-semibold whitespace-nowrap overflow-hidden text-ellipsis"
          >
            {data.children?.map(child => child.deliverable?.name).join(' + ')}
          </Typography>
          <Container className="flex flex-row items-center gap-4">
            <Typography
              variant="body-sm-regular"
              color="dark"
              className="flex flex-row items-center gap-1"
            >
              <Person />
              {data.children
                ?.map(child => child.deliverable?.usage)
                .reduce((a, b) => (a || 0) + (b || 0), 0) || 0}
            </Typography>
            <Typography
              variant="body-sm-regular"
              color="dark"
              className="flex flex-row items-center gap-1"
            >
              <Scale />
              {data.children
                ?.map(child => child.weight)
                .reduce((a, b) => (a || 0) + (b || 0), 0) || 0}
              %
            </Typography>
          </Container>
        </Container>
      </Card.Title>
      {onRemoveActionClick && (
        <div className="flex items-start">
          <Button
            variant="tertiary"
            size="icon"
            onClick={() => onRemoveActionClick([data])}
          >
            <Trash />
          </Button>
        </div>
      )}
    </Card.Header>
  );
}
