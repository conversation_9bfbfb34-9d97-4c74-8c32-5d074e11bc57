import { Badge } from '@ghq-abi/design-system';
import { Card } from '@ghq-abi/design-system-v2';

import { getTargetCardFooterText } from '~/shared/utils/getTargetCardFooterText';

import { ChildCard } from './ChildCard';
import { ParentCard } from './ParentCard';
import { TargetCardProps } from './types';

export function TargetCard({
  data,
  proposalStatus,
  currentTargetType,
  hideChildren,
  onEditTarget,
  onOpenComments,
  onRemoveActionClick,
  hasManagerPermission,
  hasEmployeePermission,
  isDrawer,
}: TargetCardProps) {
  const footerText = getTargetCardFooterText(
    proposalStatus,
    currentTargetType,
    data,
  );

  const renderFooterBadge = () => {
    if (!footerText) {
      return null;
    }

    const isAgree = footerText === 'I Agree';
    const isDisagree = footerText === "Don't agree";

    if (isAgree) {
      return (
        <Badge
          variant="tertiary"
          className="border border-green-500 text-green-500 bg-transparent"
        >
          {footerText}
        </Badge>
      );
    }

    if (isDisagree) {
      return (
        <Badge
          variant="tertiary"
          className="border border-red-500 text-red-500 bg-transparent"
        >
          {footerText}
        </Badge>
      );
    }

    return null;
  };

  return (
    <Card.Root round="md">
      <ParentCard
        data={data}
        onRemoveActionClick={onRemoveActionClick}
        isDrawer={isDrawer}
      />
      {!hideChildren && (
        <Card.Content className="flex flex-col gap-4">
          {data.children?.map(child => (
            <ChildCard
              key={child.uid}
              target={child}
              disableDrag={true}
              onEditTarget={onEditTarget}
              onOpenComments={onOpenComments}
              onRemoveActionClick={onRemoveActionClick}
              hasManagerPermission={hasManagerPermission}
              hasEmployeePermission={hasEmployeePermission}
              isDrawer={isDrawer}
            />
          ))}
        </Card.Content>
      )}
      {hasManagerPermission && (
        <Card.Footer className="flex flex-col items-end">
          <div className="px-4 pb-4">{renderFooterBadge()}</div>
        </Card.Footer>
      )}
    </Card.Root>
  );
}
