import React from 'react';
import { Container } from '@ghq-abi/design-system-v2';

import { ChildCard, TargetCard } from '~/shared/components/TargetCard';
import { Target } from '~/shared/types/Target';
import { ProposalStatusEnum } from '~/shared/utils/enums';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import { filterTargetsByType } from '../../utils/targetFilters';
import { TabEmptyState } from '../TabEmptyState';

import { ProposalDrag } from './ProposalDrag';

interface ProposalStepProps {
  targets: Target[];
  proposalUid: string;
  proposalStatus?: ProposalStatusEnum;
  hasManagerPermission?: boolean;
  hasEmployeePermission?: boolean;
  isDrawer?: boolean;
  onOpenTargetComments?: (targetId: string) => void;
}

export function ProposalStep({
  targets,
  proposalUid,
  proposalStatus,
  hasManagerPermission,
  hasEmployeePermission,
  onOpenTargetComments,
  isDrawer,
}: ProposalStepProps) {
  const proposalTargets = filterTargetsByType(targets, TargetTypeEnum.PROPOSAL);

  if (proposalTargets.length === 0 && hasEmployeePermission) {
    return (
      <TabEmptyState
        title="No proposals available"
        description="There are no proposal targets to display at this time."
      />
    );
  }

  return (
    <>
      {(proposalStatus === ProposalStatusEnum.NOT_STARTED ||
        proposalStatus === ProposalStatusEnum.IN_PROGRESS_PROPOSAL) &&
      hasManagerPermission ? (
        <ProposalDrag
          proposalStatus={proposalStatus}
          proposalUid={proposalUid}
          targets={proposalTargets}
        />
      ) : (
        <Container className="flex flex-col gap-4">
          {proposalTargets.map(target => {
            if (target.children && target.children.length > 1) {
              return (
                <TargetCard
                  key={target.uid}
                  data={target}
                  proposalStatus={proposalStatus}
                  hideChildren={target.children.length <= 1}
                  currentTargetType={TargetTypeEnum.PROPOSAL}
                  onOpenComments={target =>
                    onOpenTargetComments?.(target.uid || '')
                  }
                  hasManagerPermission={hasManagerPermission}
                  hasEmployeePermission={hasEmployeePermission}
                  isDrawer={isDrawer}
                />
              );
            }
            return (
              <ChildCard
                key={target.uid}
                target={target}
                disableDrag={true}
                onOpenComments={target =>
                  onOpenTargetComments?.(target.uid || '')
                }
                // deleteEvent={() => handleTargetRemove(`${target.uid}`)}
                hasManagerPermission={hasManagerPermission}
                hasEmployeePermission={hasEmployeePermission}
                isDrawer={isDrawer}
              />
            );
          })}
        </Container>
      )}
    </>
  );
}
