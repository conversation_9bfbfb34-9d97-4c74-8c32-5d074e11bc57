import {
  Button,
  Container,
  Drawer,
  Separator,
  Typography,
} from '@ghq-abi/design-system-v2';
import { useMutation } from '@tanstack/react-query';
import { Form, Formik } from 'formik';

import { FormikInput } from '~/shared/components/FormikInput';
import { FormikTextarea } from '~/shared/components/FormikTextarea';
import { SimpleDeliverableCard } from '~/shared/components/SimpleDeliverableCard';
import proposalService from '~/shared/services/proposal';
import { DeliverableItem } from '~/shared/types/Deliverable';
import { Proposal } from '~/shared/types/Proposal';
import { Target } from '~/shared/types/Target';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import {
  CreateEditTargetBody,
  CreateTargetDrawerProps,
  FormValues,
} from '../../types';

export const CreateTargetDrawer = ({
  proposalId,
  isOpen,
  onClose,
  isEdit = false,
  data,
  onSuccessSubmit,
  targetType = TargetTypeEnum.PROPOSAL,
}: CreateTargetDrawerProps) => {
  const initialValues = {
    definition: isEdit
      ? (data as Target)?.deliverable?.definition || ''
      : (data as DeliverableItem)?.definition || '',
    calculationMethod: isEdit
      ? (data as Target)?.deliverable?.calculationMethod || ''
      : (data as DeliverableItem)?.calculationMethod || '',
    weight: isEdit ? (data as Target)?.weight || 0 : 0,
    scope: isEdit ? (data as Target)?.scope || '' : '',
  };

  const { isLoading, mutate } = useMutation({
    mutationFn: (values: object) => {
      return proposalService.createTarget(proposalId, {
        ...values,
      });
    },
    onSuccess: (proposal: Proposal) => {
      onSuccessSubmit(proposal);
      onClose();
    },
    onError: error => {
      console.error(error);
    },
  });

  const { isLoading: isLoadingEdit, mutate: mutateEdit } = useMutation({
    mutationFn: (values: object) => {
      return proposalService.editTarget(proposalId, {
        ...values,
      });
    },
    onSuccess: (proposal: Proposal) => {
      onSuccessSubmit(proposal);
      onClose();
    },
    onError: error => {
      console.error(error);
    },
  });

  return (
    <Drawer.Root direction="right" open={isOpen} onOpenChange={onClose}>
      <Drawer.Content className="w-full max-w-lg pt-12 h-[calc(100%-74px)]">
        <Formik<FormValues>
          initialValues={initialValues}
          onSubmit={async values => {
            if (isEdit) {
              const editTargetBody: CreateEditTargetBody = {
                targets: [
                  {
                    uid: (data as Target).uid,
                    weight: Number(values.weight),
                    scope: values.scope,
                  },
                ],
              };

              mutateEdit(editTargetBody);
            } else {
              const createTargetBody: CreateEditTargetBody = {
                targets: [
                  {
                    weight: Number(values.weight),
                    scope: values.scope,
                    targetType: targetType,
                    uidDeliverable: isEdit
                      ? (data as Target)?.deliverable?.uid || ''
                      : (data as DeliverableItem).uid || '',
                  },
                ],
              };

              mutate(createTargetBody);
            }
          }}
        >
          {({ submitForm, handleChange, values }) => (
            <>
              <Container className="overflow-y-auto h-full">
                <Container className="flex justify-between items-center p-4">
                  <Container className="flex flex-col">
                    <Typography variant="body-sm-bold">
                      Create Target
                    </Typography>
                  </Container>
                </Container>
                <Separator />
                <Container className="flex flex-col py-4 px-2 gap-4">
                  <SimpleDeliverableCard
                    name={
                      isEdit
                        ? (data as Target)?.deliverable?.name || 'N/A'
                        : (data as DeliverableItem)?.name || 'N/A'
                    }
                    businessFunction={
                      isEdit
                        ? (data as Target)?.deliverable?.businessFunction ||
                          'N/A'
                        : (data as DeliverableItem)?.businessFunction || 'N/A'
                    }
                    usage={
                      isEdit
                        ? (data as Target)?.deliverable?.usage || 0
                        : (data as DeliverableItem)?.usage || 0
                    }
                    deliverableType={
                      isEdit
                        ? (data as Target)?.deliverable?.type
                        : (data as DeliverableItem)?.type
                    }
                  />
                  <Form>
                    <FormikTextarea
                      label="Definition"
                      name="definition"
                      value={values.definition}
                      onChange={handleChange}
                      placeholder="Insert here..."
                      disabled
                    />
                    <FormikTextarea
                      label="Calculation Method"
                      name="calculationMethod"
                      value={values.calculationMethod}
                      onChange={handleChange}
                      placeholder="Insert here..."
                      disabled
                    />
                    <FormikInput
                      label="Weight"
                      name="weight"
                      value={`${values.weight}`}
                      onChange={handleChange}
                      placeholder="Insert here..."
                    />
                    <FormikTextarea
                      label="Scope"
                      name="scope"
                      value={values.scope}
                      onChange={handleChange}
                      placeholder="Insert here..."
                    />
                  </Form>
                </Container>
              </Container>
              <Separator />
              <Container className="flex flex-row justify-end p-4 gap-4 w-full">
                <Button
                  variant="secondary"
                  border="default"
                  disabled={isLoading}
                  onClick={onClose}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  disabled={isLoading || isLoadingEdit}
                  isLoading={isLoading || isLoadingEdit}
                  onClick={submitForm}
                >
                  Assign
                </Button>
              </Container>
            </>
          )}
        </Formik>
      </Drawer.Content>
    </Drawer.Root>
  );
};
