import { useCatalogFilter } from '~/entities/Home/hooks/useCatalogFilter';
import { useActionModal } from '~/shared/components/ActionModal/useActionModal';
import { DeliverableItem } from '~/shared/types/Deliverable';
import { Target } from '~/shared/types/Target';
import { ProposalStatusEnum } from '~/shared/utils/enums';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

export interface FinalDragProps {
  proposalStatus?: ProposalStatusEnum;
  proposalUid: string;
  targets: Target[];
  allTargets?: Target[];
}

export interface FinalStepProps {
  targets: Target[];
  proposalUid: string;
  proposalStatus?: ProposalStatusEnum;
  hasManagerPermission: boolean;
  hasEmployeePermission: boolean;
}

export interface CatalogWithTabsProps {
  deliverables: DeliverableItem[];
  proposalTargets: Target[];
  isInitialLoading: boolean;
  isSearchLoading: boolean;
  isError: boolean;
  catalogFilterHook: ReturnType<typeof useCatalogFilter>;
  onAcceptTarget?: (targetUid: string) => void;
  acceptedTargetUids?: string[];
  isTargetLoading?: (targetUid: string) => boolean;
  showProposalTargetType?: TargetTypeEnum;
  showFeedbackTab?: boolean;
  showBadgesInProposalTab?: boolean;
}

export interface CreateTargetBody {
  targets: Target[];
}

export interface UseFinalReturn {
  // States
  selectedTargets: Target[];
  acceptedTargetUids: string[];
  loadingTargetUids: string[];
  draggedItem: DeliverableItem | Target | null;
  isDraggingNewItem: boolean;
  isOpenDrawer: boolean;
  drawerDeliverable: DeliverableItem | undefined;

  // Loading states
  isLoading: boolean;
  isLoadingDelete: boolean;
  isLoadingMergeTargets: boolean;
  isLoadingAcceptTargets: boolean;

  // Handlers
  handleAcceptTarget: (targetUid: string) => void;
  handleDragStart: (event: any) => void;
  handleDragEnd: (event: any) => void;
  handleSubmit: () => void;
  handleClearDeliverable: () => void;
  handleRemoveTargets: (targets: Target[]) => void;
  onDrawerSuccessSubmit: (proposal: any) => void;
  setIsOpenDrawer: (value: boolean) => void;
  setDrawerDeliverable: (deliverable: DeliverableItem | undefined) => void;

  // Computed values
  availableDeliverables: DeliverableItem[];
  selectedFinalTargetUids: Set<string>;

  // Helper functions
  filterTargetsByType: (
    targets: Target[],
    targetType: TargetTypeEnum,
  ) => Target[];
  isTargetLoading: (targetUid: string) => boolean;

  // Modal control
  actionModal: ReturnType<typeof useActionModal>;
}

export interface UseFinalParams {
  proposalUid: string;
  targets: Target[];
  allTargets: Target[];
  catalogFilterHook: ReturnType<typeof useCatalogFilter>;
}
