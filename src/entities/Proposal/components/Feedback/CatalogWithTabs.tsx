import React, { useState } from 'react';
import { Button, Container, Typography } from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

import { useCatalogFilter } from '~/entities/Home/hooks/useCatalogFilter';
import { ChildCard, TargetCard } from '~/shared/components';
import { DeliverableItem } from '~/shared/types/Deliverable';
import { Target } from '~/shared/types/Target';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import { CatalogListProposal } from '../CatalogListProposal';

interface CatalogWithTabsProps {
  deliverables: DeliverableItem[];
  proposalTargets: Target[];
  isInitialLoading: boolean;
  isSearchLoading: boolean;
  isError: boolean;
  catalogFilterHook: ReturnType<typeof useCatalogFilter>;
  onAcceptTarget?: (targetUid: string) => void;
  acceptedTargetUids?: string[];
  isTargetLoading?: (targetUid: string) => boolean;
  showProposalTargetType?: TargetTypeEnum;
  showFeedbackTab?: boolean;
  showBadgesInProposalTab?: boolean;
}

export function CatalogWithTabs({
  deliverables,
  proposalTargets,
  isInitialLoading,
  isSearchLoading,
  isError,
  catalogFilterHook,
  onAcceptTarget,
  acceptedTargetUids = [],
  isTargetLoading,
  showProposalTargetType = TargetTypeEnum.PROPOSAL,
  showFeedbackTab = false,
  showBadgesInProposalTab = false,
}: CatalogWithTabsProps) {
  const { t } = useTranslate();
  const [activeTab, setActiveTab] = useState<
    'catalog' | 'proposal' | 'feedback'
  >('proposal');

  const proposalTargetsFiltered = proposalTargets.filter(
    target =>
      target.targetTypes?.some(
        targetType => targetType.type === showProposalTargetType,
      ) && !acceptedTargetUids.includes(target.uid || ''),
  );

  const feedbackTargetsFiltered = proposalTargets.filter(
    target =>
      target.targetTypes?.some(
        targetType => targetType.type === TargetTypeEnum.FEEDBACK,
      ) && !acceptedTargetUids.includes(target.uid || ''),
  );

  const handleAcceptTarget = (targetUid: string) => {
    if (onAcceptTarget) {
      onAcceptTarget(targetUid);
    }
  };

  const isTargetAccepted = (targetUid: string) => {
    return acceptedTargetUids.includes(targetUid);
  };

  const ProposalTargetCard = ({ target }: { target: Target }) => {
    if (!target.uid) {
      return null;
    }

    const isAccepted = isTargetAccepted(target.uid);
    const isLoading = isTargetLoading?.(target.uid) || false;

    return (
      <div className="relative">
        {target.children && target.children.length > 1 ? (
          <TargetCard
            data={target}
            hideChildren={target.children.length <= 1}
            currentTargetType={TargetTypeEnum.PROPOSAL}
          />
        ) : (
          <ChildCard target={target} disableDrag={true} />
        )}
        <div className="absolute bottom-2 right-2">
          <Button
            variant={isAccepted ? 'secondary' : 'primary'}
            size="sm"
            onClick={() => handleAcceptTarget(target.uid!)}
            disabled={isAccepted || isLoading}
            isLoading={isLoading}
          >
            {isAccepted ? 'Accepted' : 'Accept Target'}
          </Button>
        </div>
      </div>
    );
  };

  const ProposalTargetCardWithBadges = ({ target }: { target: Target }) => {
    if (!target.uid) {
      return null;
    }

    const hasProposal = target.targetTypes?.some(
      t => t.type === TargetTypeEnum.PROPOSAL,
    );
    const hasFeedback = target.targetTypes?.some(
      t => t.type === TargetTypeEnum.FEEDBACK,
    );
    const hasFinal = target.targetTypes?.some(
      t => t.type === TargetTypeEnum.FINAL,
    );

    const isOnlyProposal = hasProposal && !hasFeedback && !hasFinal;
    const isProposalFeedback = hasProposal && hasFeedback && !hasFinal;
    const isAllThreeTypes = hasProposal && hasFeedback && hasFinal;

    const shouldShowIAgree = isProposalFeedback || isAllThreeTypes;
    const shouldShowDontAgree = isOnlyProposal;

    return (
      <div className="relative">
        {target.children && target.children.length > 1 ? (
          <TargetCard
            data={target}
            hideChildren={target.children.length <= 1}
            currentTargetType={TargetTypeEnum.PROPOSAL}
          />
        ) : (
          <ChildCard target={target} disableDrag={true} />
        )}
        <div className="absolute bottom-2 right-2 flex gap-2">
          {shouldShowIAgree && (
            <div className="px-2 py-1 border border-green-500 text-green-500 bg-transparent rounded text-xs font-medium">
              I Agree
            </div>
          )}
          {shouldShowDontAgree && (
            <div className="px-2 py-1 border border-red-500 text-red-500 bg-transparent rounded text-xs font-medium">
              Don&apos;t Agree
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <Container className="w-96 bg-white rounded-lg  flex flex-col h-full">
      {/* Tabs Header */}
      <Container className="flex gap-2 border-0 border-b border-gray-200 mb-4 flex-shrink-0">
        <Container
          className={`px-3 py-2 text-center cursor-pointer border-0 min-w-0 ${
            activeTab === 'proposal' ? 'border-b-2 border-yellow-400' : ''
          }`}
          onClick={() => setActiveTab('proposal')}
        >
          <Typography variant="body-sm-bold">Proposal</Typography>
        </Container>
        {showFeedbackTab && (
          <Container
            className={`px-3 py-2 text-center cursor-pointer border-0 min-w-0 ${
              activeTab === 'feedback' ? 'border-b-2 border-yellow-400' : ''
            }`}
            onClick={() => setActiveTab('feedback')}
          >
            <Typography variant="body-sm-bold">Feedback</Typography>
          </Container>
        )}
        <Container
          className={`px-3 py-2 text-center cursor-pointer border-0 min-w-0 ${
            activeTab === 'catalog' ? 'border-b-2 border-yellow-400' : ''
          }`}
          onClick={() => setActiveTab('catalog')}
        >
          <Typography variant="body-sm-bold">Catalog</Typography>
        </Container>
      </Container>

      {/* Tab Content */}
      <Container className="flex-1 overflow-hidden">
        {activeTab === 'proposal' ? (
          <Container className="w-96 bg-white rounded-lg p-6 flex flex-col h-full">
            <Typography
              variant="body-sm-bold"
              className="text-gray-900 mb-4 flex-shrink-0"
            >
              Proposal Targets
            </Typography>
            <Container className="flex flex-col gap-2 overflow-y-auto flex-1">
              {proposalTargetsFiltered.length === 0 ? (
                <Typography
                  variant="body-sm-regular"
                  className="text-gray-500 text-center py-8"
                >
                  No proposal targets available
                </Typography>
              ) : (
                <div className="space-y-2">
                  {proposalTargetsFiltered.map(target => (
                    <div key={target.uid} className="overflow-hidden">
                      {showBadgesInProposalTab ? (
                        <ProposalTargetCardWithBadges target={target} />
                      ) : (
                        <ProposalTargetCard target={target} />
                      )}
                    </div>
                  ))}
                </div>
              )}
            </Container>
          </Container>
        ) : activeTab === 'feedback' && showFeedbackTab ? (
          <Container className="w-96 bg-white rounded-lg p-6 flex flex-col h-full">
            <Typography
              variant="body-sm-bold"
              className="text-gray-900 mb-4 flex-shrink-0"
            >
              Feedback Targets
            </Typography>
            <Container className="flex flex-col gap-2 overflow-y-auto flex-1">
              {feedbackTargetsFiltered.length === 0 ? (
                <Typography
                  variant="body-sm-regular"
                  className="text-gray-500 text-center py-8"
                >
                  No feedback targets available
                </Typography>
              ) : (
                <div className="space-y-2">
                  {feedbackTargetsFiltered.map(target => (
                    <div key={target.uid} className="overflow-hidden">
                      <ProposalTargetCard target={target} />
                    </div>
                  ))}
                </div>
              )}
            </Container>
          </Container>
        ) : (
          <CatalogListProposal
            deliverables={deliverables}
            title={t('common_catalog')}
            isInitialLoading={isInitialLoading}
            isSearchLoading={isSearchLoading}
            isError={isError}
            catalogFilterHook={catalogFilterHook}
            containerClassName="w-96 bg-white rounded-lg p-6 flex flex-col h-full"
          />
        )}
      </Container>
    </Container>
  );
}
